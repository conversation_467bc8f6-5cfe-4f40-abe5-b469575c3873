import React, { useState, useEffect } from 'react'

const ChooseSection = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.3 }
    );

    const section = document.getElementById('journey-section');
    if (section) observer.observe(section);

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (isVisible) {
      const interval = setInterval(() => {
        setActiveStep((prev) => (prev + 1) % 4);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const journeySteps = [
    {
      id: 1,
      title: "Dream & Discover",
      subtitle: "Your Adventure Begins",
      description: "Browse our curated destinations and let your wanderlust guide you to the perfect North East India experience.",
      icon: "fas fa-compass",
      color: "from-purple-500 to-indigo-600",
      bgColor: "bg-purple-50",
      delay: "0ms"
    },
    {
      id: 2,
      title: "Plan & Personalize",
      subtitle: "Crafted Just for You",
      description: "Our local experts work with you to create a personalized itinerary that matches your interests and budget.",
      icon: "fas fa-route",
      color: "from-blue-500 to-cyan-600",
      bgColor: "bg-blue-50",
      delay: "200ms"
    },
    {
      id: 3,
      title: "Experience & Explore",
      subtitle: "Live the Magic",
      description: "Immerse yourself in authentic local culture, breathtaking landscapes, and unforgettable moments.",
      icon: "fas fa-mountain",
      color: "from-green-500 to-emerald-600",
      bgColor: "bg-green-50",
      delay: "400ms"
    },
    {
      id: 4,
      title: "Remember & Return",
      subtitle: "Memories for Life",
      description: "Take home incredible memories and join our community of travelers who keep coming back for more.",
      icon: "fas fa-heart",
      color: "from-red-500 to-pink-600",
      bgColor: "bg-red-50",
      delay: "600ms"
    }
  ];

  return (
    <div>
      <section id="journey-section" className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
        {/* Background Decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-200/30 to-blue-200/30 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-200/30 to-cyan-200/30 rounded-full blur-3xl"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-block mb-4">
              <span className="text-slate-400 text-sm font-bold uppercase tracking-wider">
                Your Journey With Us
              </span>
            </div>
            <h2 className="text-4xl md:text-5xl font-light font-serif text-black mb-6">
              From Dream to{" "}
              <span className="text-blue-400 font-bold">
                Reality
              </span>
            </h2>
            <p className="text-xl text-black max-w-3xl mx-auto leading-relaxed">
              Experience how we transform your travel dreams into extraordinary adventures through our unique 4-step journey
            </p>
          </div>

          {/* Interactive Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-purple-200 via-green-200 to-red-200 transform -translate-y-1/2 rounded-full"></div>

            {/* Progress Line */}
            <div
              className="hidden lg:block absolute top-1/2 left-0 h-1 bg-gradient-to-r from-purple-500 to-blue-500 transform -translate-y-1/2 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${((activeStep + 1) / 4) * 100}%` }}
            ></div>

            {/* Journey Steps */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-4">
              {journeySteps.map((step, index) => (
                <div
                  key={step.id}
                  className={`relative group cursor-pointer transition-all duration-700 ${
                    isVisible ? 'animate-fadeInUp' : 'opacity-0'
                  }`}
                  style={{ animationDelay: step.delay }}
                  onClick={() => setActiveStep(index)}
                >
                  {/* Timeline Dot */}
                  <div className="hidden lg:flex absolute -top-6 left-1/2 transform -translate-x-1/2 z-20">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 ${
                      activeStep >= index
                        ? `bg-gradient-to-r ${step.color} shadow-lg scale-110`
                        : 'bg-white border-4 border-gray-300'
                    }`}>
                      <i className={`${step.icon} text-lg ${
                        activeStep >= index ? 'text-white' : 'text-gray-400'
                      }`}></i>
                    </div>
                  </div>

                  {/* Card */}
                  <div className={`relative mt-8 lg:mt-12 p-8 rounded-2xl transition-all duration-500 transform ${
                    activeStep === index
                      ? `${step.bgColor} shadow-2xl scale-105 border-2 border-opacity-30`
                      : 'bg-white shadow-lg hover:shadow-xl hover:-translate-y-2'
                  } group-hover:scale-105`}>

                    {/* Mobile Icon */}
                    <div className="lg:hidden flex justify-center mb-6">
                      <div className={`w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-r ${step.color} shadow-lg`}>
                        <i className={`${step.icon} text-2xl text-white`}></i>
                      </div>
                    </div>

                    {/* Step Number */}
                    <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-gray-800 to-gray-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                      {step.id}
                    </div>

                    {/* Content */}
                    <div className="text-center lg:text-left">
                      <h3 className={`text-2xl font-bold mb-2 transition-colors duration-300 ${
                        activeStep === index
                          ? 'bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent'
                          : 'text-gray-900'
                      }`}>
                        {step.title}
                      </h3>
                      <p className={`text-sm font-semibold mb-4 transition-colors duration-300 ${
                        activeStep === index ? 'text-purple-600' : 'text-gray-500'
                      }`}>
                        {step.subtitle}
                      </p>
                      <p className="text-gray-700 leading-relaxed">
                        {step.description}
                      </p>
                    </div>

                    {/* Hover Effect */}
                    <div className={`absolute inset-0 bg-gradient-to-r ${step.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="inline-flex items-center space-x-4 bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
              <div className="flex -space-x-2">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className={`w-10 h-10 rounded-full bg-gradient-to-r ${journeySteps[i-1].color} flex items-center justify-center border-2 border-white`}>
                    <i className={`${journeySteps[i-1].icon} text-white text-sm`}></i>
                  </div>
                ))}
              </div>
              <div className="text-left">
                <p className="text-lg font-bold text-gray-900">Ready to start your journey?</p>
                <p className="text-gray-600">Let's create your perfect North East India adventure</p>
              </div>
              <a
                href="/plan"
                className="bg-blue-400 text-white px-8 py-3 rounded-xl font-semibold hover:bg-blue-500 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                Start Planning
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

export default ChooseSection