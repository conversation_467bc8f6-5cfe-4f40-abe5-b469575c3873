import { useState } from 'react';
import animation11 from '../../assets/photos/animation11.png';

const VideoSection = () => {
  const [playingVideo, setPlayingVideo] = useState(null);
  const [hoveredVideo, setHoveredVideo] = useState(null);
  const [likedVideos, setLikedVideos] = useState(new Set());
  const [videoRatings, setVideoRatings] = useState(new Map());
  const [sharedVideos, setSharedVideos] = useState(new Set());

  // Video gallery data - Only 2 cards
  const videos = [
    {
      id: 1,
      youtubeId: 'rvmEz9RiDZ8',
      title: 'Discover North East India',
      description: 'Experience the magical landscapes and rich culture',
      thumbnail: animation11,
      duration: '3:45'
    },
    {
      id: 2,
      youtubeId: 'dQw4w9WgXcQ', // Replace with actual video IDs
      title: 'Sikkim Adventures',
      description: 'Journey through the pristine mountains of Sikkim',
      thumbnail: animation11,
      duration: '5:20'
    }
  ];

  const handlePlayVideo = (videoId) => {
    setPlayingVideo(videoId);
  };

  const handleCloseVideo = () => {
    setPlayingVideo(null);
  };

  const handleLikeVideo = (videoId) => {
    setLikedVideos(prev => {
      const newLiked = new Set(prev);
      if (newLiked.has(videoId)) {
        newLiked.delete(videoId);
      } else {
        newLiked.add(videoId);
      }
      return newLiked;
    });
  };

  const handleRateVideo = (videoId, rating) => {
    setVideoRatings(prev => {
      const newRatings = new Map(prev);
      newRatings.set(videoId, rating);
      return newRatings;
    });
  };

  const handleShareVideo = (videoId) => {
    const video = videos.find(v => v.id === videoId);
    if (video) {
      // Simple share functionality - copy to clipboard
      const shareUrl = `https://www.youtube.com/watch?v=${video.youtubeId}`;
      navigator.clipboard.writeText(shareUrl).then(() => {
        setSharedVideos(prev => new Set(prev).add(videoId));
        // Reset shared state after 2 seconds
        setTimeout(() => {
          setSharedVideos(prev => {
            const newShared = new Set(prev);
            newShared.delete(videoId);
            return newShared;
          });
        }, 2000);
      });
    }
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light mb-4 font-serif">
            Discover the{" "}
            <span className='text-blue-400'>Magic</span> of North East India
          </h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="max-w-xl mx-auto text-gray-600">
            Experience the beauty of North East India through our curated video collection
          </p>
        </div>

        {/* Enhanced Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-10 max-w-7xl mx-auto">
          {videos.map((video) => (
            <div
              key={video.id}
              className={`group bg-gradient-to-br from-white to-gray-50 rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 hover:rotate-1 border border-gray-100 ${
                playingVideo === video.id ? 'ring-4 ring-purple-500 shadow-purple-200 p-4 bg-gradient-to-br from-purple-50 to-indigo-50' : ''
              }`}
              onMouseEnter={() => setHoveredVideo(video.id)}
              onMouseLeave={() => setHoveredVideo(null)}
            >
              {/* Enhanced Video Thumbnail */}
              <div className="relative h-72 overflow-hidden rounded-t-3xl">
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 filter group-hover:brightness-110"
                />

                {/* Enhanced Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Stylized Duration Badge */}
                <div className="absolute top-4 right-4 bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-bold backdrop-blur-sm shadow-lg">
                  {video.duration}
                </div>

                {/* Enhanced Play Button */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                  <button
                    onClick={() => handlePlayVideo(video.id)}
                    className="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-2xl border-4 border-white/30"
                  >
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-1 text-white"
                    >
                      <path d="M8 5v14l11-7-11-7z" fill="currentColor" />
                    </svg>
                  </button>
                </div>

                {/* Decorative Corner Element */}
                <div className="absolute top-0 left-0 w-16 h-16 bg-gradient-to-br from-yellow-400/20 to-transparent rounded-br-full"></div>
              </div>

              {/* Enhanced Video Info */}
              <div className="p-8 bg-gradient-to-br from-white to-gray-50">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4 group-hover:from-purple-700 group-hover:to-indigo-700 transition-all duration-300">
                  {video.title}
                </h3>
                <p className="text-gray-700 text-base leading-relaxed mb-6 font-medium">
                  {video.description}
                </p>

                {/* Action Buttons */}
                <div className="flex justify-center space-x-3">
                  {/* Like Button */}
                  <button
                    onClick={() => handleLikeVideo(video.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 ${
                      likedVideos.has(video.id)
                        ? 'bg-red-500 text-white'
                        : 'bg-white border border-gray-300 text-gray-700 hover:bg-red-50 hover:border-red-300'
                    }`}
                  >
                    <svg
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill={likedVideos.has(video.id) ? "currentColor" : "none"}
                      stroke="currentColor"
                      strokeWidth="2"
                      className="transition-all duration-300"
                    >
                      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
                    </svg>
                    <span className="text-sm font-medium">
                      {likedVideos.has(video.id) ? 'Liked' : 'Like'}
                    </span>
                  </button>

                  {/* Rating System */}
                  <div className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-white border border-gray-300 shadow-md hover:shadow-lg transition-all duration-300">
                    <span className="text-sm font-medium text-gray-700">Rate:</span>
                    <div className="flex space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          onClick={() => handleRateVideo(video.id, star)}
                          className="transition-all duration-200 hover:scale-110"
                        >
                          <svg
                            width="18"
                            height="18"
                            viewBox="0 0 24 24"
                            fill={videoRatings.get(video.id) >= star ? "#fbbf24" : "none"}
                            stroke="#fbbf24"
                            strokeWidth="2"
                            className="transition-all duration-200"
                          >
                            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26" />
                          </svg>
                        </button>
                      ))}
                    </div>
                    {videoRatings.has(video.id) && (
                      <span className="text-sm text-yellow-600 font-medium">
                        {videoRatings.get(video.id)}/5
                      </span>
                    )}
                  </div>

                  {/* Share Button */}
                  <button
                    onClick={() => handleShareVideo(video.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 ${
                      sharedVideos.has(video.id)
                        ? 'bg-green-500 text-white'
                        : 'bg-white border border-gray-300 text-gray-700 hover:bg-green-50 hover:border-green-300'
                    }`}
                  >
                    <svg
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="transition-all duration-300"
                    >
                      <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
                      <polyline points="16,6 12,2 8,6" />
                      <line x1="12" y1="2" x2="12" y2="15" />
                    </svg>
                    <span className="text-sm font-medium">
                      {sharedVideos.has(video.id) ? 'Copied!' : 'Share'}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Netflix-Style Video Modal */}
      {playingVideo && (
        <div className="fixed inset-0 z-[9999] bg-black flex items-center justify-center animate-fadeIn">
          <div className="relative w-full max-w-6xl mx-auto animate-slideUp">
            {/* Video Container - Netflix Style */}
            <div className="relative w-full bg-black rounded-lg overflow-hidden shadow-2xl" style={{ paddingTop: "56.25%" }}>
              {/* Close Button - Positioned on video like Netflix */}
              <button
                onClick={handleCloseVideo}
                className="absolute top-4 right-4 z-20 w-10 h-10 bg-black/70 hover:bg-black/90 text-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110"
              >
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </button>

              <iframe
                className="absolute inset-0 w-full h-full"
                src={`https://www.youtube.com/embed/${videos.find(v => v.id === playingVideo)?.youtubeId}?autoplay=1&rel=0&modestbranding=0&playsinline=1&controls=1&cc_load_policy=1&iv_load_policy=3&enablejsapi=1&origin=${window.location.origin}`}
                title="YouTube video player"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                style={{ border: 'none' }}
              ></iframe>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default VideoSection;
